import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/models/currency/currency_model.dart';
import 'package:culture_connect/models/currency/exchange_rate_model.dart';
import 'package:culture_connect/models/currency/currency_conversion_history_model.dart';
import 'package:culture_connect/models/currency/currency_preference_model.dart';
import 'package:culture_connect/services/currency/currency_data_service.dart';
import 'package:culture_connect/services/currency/exchange_rate_api_service.dart';
import 'package:culture_connect/services/currency/currency_conversion_service.dart';
import 'package:culture_connect/providers/shared_preferences_provider.dart';
import 'package:culture_connect/providers/services_providers.dart';

/// Provider for the currency data service
final currencyDataServiceProvider = Provider<CurrencyDataService>((ref) {
  return CurrencyDataService();
});

/// Provider for the exchange rate API service
final exchangeRateApiServiceProvider = Provider<ExchangeRateApiService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final client = http.Client();
  final exchangeRateCache = Hive.box<String>('exchange_rate_cache');
  final historicalRateCache = Hive.box<String>('historical_rate_cache');

  final service = ExchangeRateApiService(
    baseUrl: 'https://api.exchangerateost',
    apiKey: '', // No API key needed for this demo API
    client: client,
    loggingService: loggingService,
    exchangeRateCache: exchangeRateCache,
    historicalRateCache: historicalRateCache,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for the currency conversion service
final currencyConversionServiceProvider =
    Provider<CurrencyConversionService>((ref) {
  final exchangeRateApiService = ref.watch(exchangeRateApiServiceProvider);
  final currencyDataService = ref.watch(currencyDataServiceProvider);
  final loggingService = ref.watch(loggingServiceProvider);
  final locationService = ref.watch(locationServiceProvider);
  final preferences = ref.watch(sharedPreferencesProvider);

  final service = CurrencyConversionService(
    exchangeRateApiService: exchangeRateApiService,
    currencyDataService: currencyDataService,
    loggingService: loggingService,
    locationService: locationService,
    preferences: preferences,
  );

  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for currency preferences
final currencyPreferencesProvider =
    StreamProvider<CurrencyPreferenceModel>((ref) {
  final service = ref.watch(currencyConversionServiceProvider);
  return service.currencyPreferenceStream;
});

/// Provider for the current currency preferences
final currentCurrencyPreferencesProvider =
    Provider<CurrencyPreferenceModel>((ref) {
  final service = ref.watch(currencyConversionServiceProvider);
  return service.currencyPreferences;
});

/// Provider for all supported currencies
final allCurrenciesProvider = Provider<List<CurrencyModel>>((ref) {
  final service = ref.watch(currencyDataServiceProvider);
  return service.getAllCurrencies();
});

/// Provider for major currencies
final majorCurrenciesProvider = Provider<List<CurrencyModel>>((ref) {
  final service = ref.watch(currencyDataServiceProvider);
  return service.getMajorCurrencies();
});

/// Provider for favorite currencies
final favoriteCurrenciesProvider = Provider<List<CurrencyModel>>((ref) {
  final service = ref.watch(currencyDataServiceProvider);
  final preferences = ref.watch(currentCurrencyPreferencesProvider);

  return preferences.favoriteCurrencies
      .map((code) => service.getCurrencyByCode(code))
      .whereType<CurrencyModel>()
      .toList();
});

/// Provider for recently used currencies
final recentlyUsedCurrenciesProvider = Provider<List<CurrencyModel>>((ref) {
  final service = ref.watch(currencyDataServiceProvider);
  final preferences = ref.watch(currentCurrencyPreferencesProvider);

  return preferences.recentlyUsedCurrencies
      .map((code) => service.getCurrencyByCode(code))
      .whereType<CurrencyModel>()
      .toList();
});

/// Provider for the preferred currency
final preferredCurrencyProvider = Provider<CurrencyModel>((ref) {
  final service = ref.watch(currencyDataServiceProvider);
  final preferences = ref.watch(currentCurrencyPreferencesProvider);

  final currency = service.getCurrencyByCode(preferences.preferredCurrency);
  if (currency != null) {
    return currency;
  } else {
    // Fallback to USD if the preferred currency is not found
    return service.getCurrencyByCode('USD')!;
  }
});

/// Provider for exchange rates with a specific base currency
final exchangeRatesProvider =
    FutureProvider.family<ExchangeRatesCollection, String>(
        (ref, baseCurrency) async {
  final service = ref.watch(exchangeRateApiServiceProvider);
  return service.getLatestRates(baseCurrency);
});

/// Provider for a specific exchange rate
final exchangeRateProvider =
    FutureProvider.family<ExchangeRateModel, (String, String)>(
        (ref, params) async {
  final service = ref.watch(exchangeRateApiServiceProvider);
  final (baseCurrency, targetCurrency) = params;
  return service.getExchangeRate(baseCurrency, targetCurrency);
});

/// Provider for historical exchange rates
final historicalRatesProvider = FutureProvider.family<
    CurrencyConversionHistoryModel,
    (String, String, DateTime?, DateTime?)>((ref, params) async {
  final service = ref.watch(exchangeRateApiServiceProvider);
  final (baseCurrency, targetCurrency, startDate, endDate) = params;
  return service.getHistoricalRates(
    baseCurrency,
    targetCurrency,
    startDate: startDate,
    endDate: endDate,
  );
});

/// Provider for converting an amount from one currency to another
final currencyConversionProvider =
    FutureProvider.family<double, (double, String, String)>(
        (ref, params) async {
  final service = ref.watch(currencyConversionServiceProvider);
  final (amount, fromCurrency, toCurrency) = params;
  return service.convertCurrency(amount, fromCurrency, toCurrency);
});

/// Provider for formatting an amount according to a currency's rules
final currencyFormattingProvider =
    Provider.family<String, (String, double)>((ref, params) {
  final service = ref.watch(currencyConversionServiceProvider);
  final (currencyCode, amount) = params;
  return service.formatAmount(currencyCode, amount);
});

/// Provider for formatting an amount according to a currency's rules and the user's locale
final currencyFormattingWithLocaleProvider =
    Provider.family<String, (String, double, String)>((ref, params) {
  final service = ref.watch(currencyConversionServiceProvider);
  final (currencyCode, amount, locale) = params;
  return service.formatAmountWithLocale(currencyCode, amount, locale);
});
