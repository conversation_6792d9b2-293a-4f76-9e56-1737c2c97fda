import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/services/travel/restaurant_reservation_service.dart';
import 'package:culture_connect/providers/auth_provider.dart';

/// Provider for the RestaurantReservationService
final restaurantReservationServiceProvider =
    Provider<RestaurantReservationService>((ref) {
  return RestaurantReservationService();
});

/// Provider for all reservations of the current user
final userReservationsProvider =
    FutureProvider<List<RestaurantReservation>>((ref) async {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    return [];
  }

  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getUserReservations(user.id);
});

/// Provider for upcoming reservations of the current user
final upcomingReservationsProvider =
    FutureProvider<List<RestaurantReservation>>((ref) async {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    return [];
  }

  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getUpcomingReservations(user.id);
});

/// Provider for past reservations of the current user
final pastReservationsProvider =
    FutureProvider<List<RestaurantReservation>>((ref) async {
  final user = ref.watch(authStateProvider).user;
  if (user == null) {
    return [];
  }

  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getPastReservations(user.id);
});

/// Provider for a specific reservation by ID
final reservationByIdProvider =
    FutureProvider.family<RestaurantReservation?, String>(
        (ref, reservationId) async {
  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getReservationById(reservationId);
});

/// Provider for reservations for a specific restaurant
final restaurantReservationsProvider =
    FutureProvider.family<List<RestaurantReservation>, String>(
        (ref, restaurantId) async {
  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getReservationsForRestaurant(restaurantId);
});

/// Provider for available time slots for a specific restaurant on a specific date
final availableTimeSlotsProvider = FutureProvider.family<
    List<RestaurantTimeSlot>,
    ({String restaurantId, DateTime date})>((ref, params) async {
  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return reservationService.getAvailableTimeSlots(
      params.restaurantId, params.date);
});

/// Notifier for managing restaurant reservations
class RestaurantReservationNotifier
    extends StateNotifier<AsyncValue<RestaurantReservation?>> {
  final RestaurantReservationService _reservationService;

  RestaurantReservationNotifier(this._reservationService)
      : super(const AsyncValue.loading());

  /// Create a new reservation
  Future<void> createReservation({
    required String restaurantId,
    required String restaurantName,
    required DateTime date,
    required RestaurantTimeSlot timeSlot,
    required int partySize,
    String specialRequests = '',
    required String userId,
    required String userName,
    required String contactPhone,
    required String contactEmail,
  }) async {
    try {
      state = const AsyncValue.loading();

      final reservation = RestaurantReservation(
        thisestaurantId: restaurantId,
        thisestaurantName: restaurantName,
        date: date,
        timeSlot: timeSlot,
        partySize: partySize,
        thisecialRequests: specialRequests,
        userId: userId,
        userName: userName,
        contactPhone: contactPhone,
        contactEmail: contactEmail,
      );

      final createdReservation =
          await _reservationService.createReservation(reservation);
      state = AsyncValue.data(createdReservation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Update an existing reservation
  Future<void> updateReservation({
    required String reservationId,
    DateTime? date,
    RestaurantTimeSlot? timeSlot,
    int? partySize,
    String? specialRequests,
    ReservationStatus? status,
  }) async {
    try {
      state = const AsyncValue.loading();

      final existingReservation =
          await _reservationService.getReservationById(reservationId);
      if (existingReservation == null) {
        throw Exception('Reservation not found');
      }

      final updatedReservation = existingReservation.copyWith(
        date: date,
        timeSlot: timeSlot,
        partySize: partySize,
        specialRequests: specialRequests,
        status: status,
        updatedAt: DateTime.now(),
      );

      final result =
          await _reservationService.updateReservation(updatedReservation);
      state = AsyncValue.data(result);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Cancel a reservation
  Future<void> cancelReservation(String reservationId) async {
    try {
      state = const AsyncValue.loading();

      final existingReservation =
          await _reservationService.getReservationById(reservationId);
      if (existingReservation == null) {
        throw Exception('Reservation not found');
      }

      final cancelledReservation = existingReservation.copyWith(
        status: ReservationStatus.cancelled,
        updatedAt: DateTime.now(),
      );

      final result =
          await _reservationService.updateReservation(cancelledReservation);
      state = AsyncValue.data(result);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Load a reservation by ID
  Future<void> loadReservation(String reservationId) async {
    try {
      state = const AsyncValue.loading();
      final reservation =
          await _reservationService.getReservationById(reservationId);
      state = AsyncValue.data(reservation);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// Provider for the RestaurantReservationNotifier
final restaurantReservationNotifierProvider = StateNotifierProvider<
    RestaurantReservationNotifier, AsyncValue<RestaurantReservation?>>((ref) {
  final reservationService = ref.watch(restaurantReservationServiceProvider);
  return RestaurantReservationNotifier(reservationService);
});

/// Provider for the selected date for a reservation
final selectedReservationDateProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

/// Provider for the selected time slot for a reservation
final selectedTimeSlotProvider = StateProvider<RestaurantTimeSlot?>((ref) {
  return null;
});

/// Provider for the selected party size for a reservation
final selectedPartySizeProvider = StateProvider<int>((ref) {
  return 2;
});

/// Provider for the special requests for a reservation
final specialRequestsProvider = StateProvider<String>((ref) {
  return '';
});
