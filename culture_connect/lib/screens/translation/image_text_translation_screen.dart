import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import 'package:culture_connect/models/translation/image_text_translation_model.dart';
import 'package:culture_connect/providers/image_text_translation_provider.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/services/image_text_translation_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/custom_app_bar.dart';
import 'package:culture_connect/widgets/loading_indicator.dart';
import 'package:culture_connect/widgets/translation/recognized_text_overlay.dart';
import 'package:culture_connect/widgets/translation/translated_text_display.dart';
import 'package:culture_connect/widgets/voice_translation/language_selector.dart';

/// A screen for translating text from images
class ImageTextTranslationScreen extends ConsumerStatefulWidget {
  /// Creates a new image text translation screen
  const ImageTextTranslationScreen({super.key});

  @override
  ConsumerState<ImageTextTranslationScreen> createState() =>
      _ImageTextTranslationScreenState();
}

class _ImageTextTranslationScreenState
    extends ConsumerState<ImageTextTranslationScreen>
    with SingleTickerProviderStateMixin {
  bool _showOverlay = true;
  bool _showOriginalText = true;
  late TabController _tabController;
  Size? _imageSize;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(imageTextTranslationProvider);
    final targetLanguage = ref.watch(targetLanguageProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Image Text Translation',
        showBackButton: true,
        actions: [
          // History button
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.pushNamed(context, '/image-text-translation/history');
            },
            tooltip: 'Translation History',
          ),

          // Cultural context settings button
          IconButton(
            icon: const Icon(Icons.psychology),
            onPressed: () {
              Navigator.pushNamed(context, '/cultural-context-settings');
            },
            tooltip: 'Cultural Context Settings',
          ),

          // Settings button
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Column(
        children: [
          // Language selector
          Padding(
            padding: EdgeInsets.all(16),
            child: const LanguageSelector(),
          ),

          // Tab bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Camera'),
              Tab(text: 'Gallery'),
            ],
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.textSecondaryColor,
            indicatorColor: AppTheme.primaryColor,
          ),

          // Error message
          if (state.errorMessage != null)
            Container(
              width: double.infinity,
              margin: EdgeInsets.all(16),
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.red,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      state.errorMessage!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red[800],
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    color: Colors.red,
                    onPressed: () {
                      ref
                          .read(imageTextTranslationProvider.notifier)
                          .clearError();
                    },
                  ),
                ],
              ),
            ).animate().fadeIn().slideY(begin: -0.1, end: 0),

          // Content
          Expanded(
            child: state.isLoading
                ? const Center(child: LoadingIndicator())
                : state.currentTranslation != null
                    ? _buildTranslationView(state.currentTranslation!)
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          // Camera tab
                          _buildCameraTab(targetLanguage.code),

                          // Gallery tab
                          _buildGalleryTab(targetLanguage.code),
                        ],
                      ),
          ),
        ],
      ),
    );
  }

  /// Build the camera tab
  Widget _buildCameraTab(String targetLanguage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.camera_alt,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'Take a photo to translate text',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Point your camera at text in any language\nand we\'ll translate it for you',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              ref
                  .read(imageTextTranslationProvider.notifier)
                  .takePhoto(targetLanguage);
            },
            icon: const Icon(Icons.camera_alt),
            label: const Text('Take Photo'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the gallery tab
  Widget _buildGalleryTab(String targetLanguage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'Select an image to translate text',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Choose an image from your gallery\ncontaining text in any language',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: () {
              ref
                  .read(imageTextTranslationProvider.notifier)
                  .pickImageFromGallery(targetLanguage);
            },
            icon: const Icon(Icons.photo_library),
            label: const Text('Select Image'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the translation view
  Widget _buildTranslationView(ImageTextTranslationModel translation) {
    return Column(
      children: [
        // Image with overlay
        Expanded(
          child: Stack(
            children: [
              // Image
              Center(
                child: InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 3.0,
                  child: Image.file(
                    File(translation.imagePath),
                    fit: BoxFit.contain,
                    frameBuilder:
                        (context, child, frame, wasSynchronouslyLoaded) {
                      if (frame != null) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _updateImageSize(context);
                        });
                      }
                      return child;
                    },
                  ),
                ),
              ),

              // Text overlay
              if (_imageSize != null &&
                  translation.recognizedTextBlocks.isNotEmpty)
                RecognizedTextOverlay(
                  textBlocks: translation.recognizedTextBlocks,
                  imageSize: _imageSize!,
                  showOverlay: _showOverlay,
                  onTextBlockTap: (block) {
                    _showTextBlockDialog(block);
                  },
                ),

              // Overlay toggle button
              Positioned(
                top: 16,
                right: 16,
                child: FloatingActionButton.small(
                  onPressed: () {
                    setState(() {
                      _showOverlay = !_showOverlay;
                    });
                  },
                  backgroundColor: Colors.white.withAlpha(204),
                  child: Icon(
                    _showOverlay ? Icons.visibility : Icons.visibility_off,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),

              // New translation button
              Positioned(
                bottom: 16,
                right: 16,
                child: FloatingActionButton(
                  onPressed: () {
                    ref
                        .read(imageTextTranslationProvider.notifier)
                        .clearCurrentTranslation();
                  },
                  backgroundColor: AppTheme.primaryColor,
                  child: const Icon(Icons.add_photo_alternate),
                ),
              ),
            ],
          ),
        ),

        // Translated text
        TranslatedTextDisplay(
          translation: translation,
          showOriginalText: _showOriginalText,
          onCopyTranslation: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Translation copied to clipboard'),
                duration: Duration(seconds: 2),
              ),
            );
          },
          onShareTranslation: () {
            _shareTranslation(translation);
          },
          onToggleFavorite: () {
            ref
                .read(imageTextTranslationProvider.notifier)
                .toggleFavorite(translation.id);
          },
        ),
      ],
    );
  }

  /// Update the image size
  void _updateImageSize(BuildContext context) {
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      setState(() {
        _imageSize = renderBox.size;
      });
    }
  }

  /// Show a dialog with the text block details
  void _showTextBlockDialog(RecognizedTextBlock block) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Text Block'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Text:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                block.text,
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
            ),
            SizedBox(height: 16),
            if (block.languageCode != null) ...[
              Text(
                'Language:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    _getLanguageFlag(block.languageCode!),
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    _getLanguageName(block.languageCode!),
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ],
            SizedBox(height: 16),
            Text(
              'Confidence:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 4),
            LinearProgressIndicator(
              value: block.confidence,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                _getConfidenceColor(block.confidence),
              ),
            ),
            SizedBox(height: 4),
            Text(
              '${(block.confidence * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                color: _getConfidenceColor(block.confidence),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Show the settings dialog
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final notifier = ref.read(imageTextTranslationProvider.notifier);
          final service = ref.read(imageTextTranslationServiceProvider);

          return AlertDialog(
            title: const Text('Settings'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Offline mode
                SwitchListTile(
                  title: const Text('Offline Mode'),
                  subtitle: const Text('Translate without internet connection'),
                  value: service.useOfflineMode,
                  onChanged: (value) {
                    setState(() {
                      notifier.setUseOfflineMode(value);
                    });
                  },
                ),

                // Custom vocabulary
                SwitchListTile(
                  title: const Text('Custom Vocabulary'),
                  subtitle:
                      const Text('Use your custom vocabulary for translations'),
                  value: service.useCustomVocabulary,
                  onChanged: (value) {
                    setState(() {
                      notifier.setUseCustomVocabulary(value);
                    });
                  },
                ),

                // Cultural context
                SwitchListTile(
                  title: const Text('Cultural Context'),
                  subtitle: const Text(
                      'Show cultural context information with translations'),
                  value: service.useCulturalContext,
                  onChanged: (value) {
                    setState(() {
                      service.setUseCulturalContext(value);
                    });
                  },
                ),

                // Show original text
                SwitchListTile(
                  title: const Text('Show Original Text'),
                  subtitle: const Text(
                      'Display the original text alongside the translation'),
                  value: _showOriginalText,
                  onChanged: (value) {
                    setState(() {
                      _showOriginalText = value;
                    });

                    if (mounted) {
                      this.setState(() {});
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Close'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Share the translation
  void _shareTranslation(ImageTextTranslationModel translation) async {
    if (translation.translatedText == null) return;

    final text = translation.sourceLanguage != null
        ? 'Original (${_getLanguageName(translation.sourceLanguage!)}): ${translation.recognizedText}\n\nTranslation (${_getLanguageName(translation.targetLanguage)}): ${translation.translatedText}'
        : 'Translation (${_getLanguageName(translation.targetLanguage)}): ${translation.translatedText}';

    await Share.share(text, subject: 'Translated Text from CultureConnect');
  }

  /// Get the flag for a language code
  String _getLanguageFlag(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return '🇺🇸';
      case 'fr':
        return '🇫🇷';
      case 'es':
        return '🇪🇸';
      case 'de':
        return '🇩🇪';
      case 'it':
        return '🇮🇹';
      case 'pt':
        return '🇵🇹';
      case 'ru':
        return '🇷🇺';
      case 'zh':
        return '🇨🇳';
      case 'ja':
        return '🇯🇵';
      case 'ko':
        return '🇰🇷';
      case 'ar':
        return '🇸🇦';
      case 'hi':
        return '🇮🇳';
      case 'bn':
        return '🇧🇩';
      case 'sw':
        return '🇰🇪';
      case 'yo':
        return '🇳🇬';
      case 'ha':
        return '🇳🇬';
      case 'ig':
        return '🇳🇬';
      default:
        return '🌐';
    }
  }

  /// Get the name for a language code
  String _getLanguageName(String languageCode) {
    // This is a simplified version - in a real app, you would use a more comprehensive mapping
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'de':
        return 'German';
      case 'it':
        return 'Italian';
      case 'pt':
        return 'Portuguese';
      case 'ru':
        return 'Russian';
      case 'zh':
        return 'Chinese';
      case 'ja':
        return 'Japanese';
      case 'ko':
        return 'Korean';
      case 'ar':
        return 'Arabic';
      case 'hi':
        return 'Hindi';
      case 'bn':
        return 'Bengali';
      case 'sw':
        return 'Swahili';
      case 'yo':
        return 'Yoruba';
      case 'ha':
        return 'Hausa';
      case 'ig':
        return 'Igbo';
      default:
        return languageCode;
    }
  }

  /// Get the color for a confidence score
  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) {
      return Colors.green;
    } else if (confidence >= 0.6) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
