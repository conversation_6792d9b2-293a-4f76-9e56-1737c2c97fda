import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Flutter imports
import 'package:flutter/foundation.dart';

// Package imports
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

/// Log level enum for different severity levels
enum LogLevel {
  verbose(0, 'VERBOSE', '💬'),
  debug(1, 'DEBUG', '🐛'),
  info(2, 'INFO', 'ℹ️'),
  warning(3, 'WARNING', '⚠️'),
  error(4, 'ERROR', '❌'),
  critical(5, 'CRITICAL', '🔥');

  final int value;
  final String name;
  final String emoji;

  const LogLevel(this.value, this.name, this.emoji);
}

/// Provider for the LoggingService
final loggingServiceProvider = Provider<LoggingService>((ref) {
  return LoggingService();
});

/// A comprehensive logging service for the application
class LoggingService {
  static final LoggingService _instance = LoggingService._internal();

  /// Factory constructor to return the singleton instance
  factory LoggingService() => _instance;

  LoggingService._internal();

  /// Current log level - logs below this level will not be processed
  LogLevel _currentLogLevel = kDebugMode ? LogLevel.verbose : LogLevel.info;

  /// Whether to log to console
  bool _logToConsole = true;

  /// Whether to log to file
  bool _logToFile = !kDebugMode;

  /// Whether to log to crashlytics
  bool _logToCrashlytics = !kDebugMode;

  /// Maximum log file size in bytes (5MB)
  int _maxLogFileSize = 5 * 1024 * 1024;

  /// Maximum number of log files to keep
  int _maxLogFiles = 5;

  /// Log file path
  String? _logFilePath;

  /// Log buffer for batch writing
  final List<String> _logBuffer = [];

  /// Timer for flushing logs to file
  Timer? _flushTimer;

  /// Device information
  Map<String, dynamic>? _deviceInfo;

  /// App information
  PackageInfo? _packageInfo;

  /// Initialize the logging service
  Future<void> initialize() async {
    try {
      // Get device info
      final deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        _deviceInfo = {
          'device': androidInfo.device,
          'manufacturer': androidInfo.manufacturer,
          'model': androidInfo.model,
          'version': androidInfo.versionelease,
          'sdkInt': androidInfo.version.sdkInt,
          'platform': 'android',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        _deviceInfo = {
          'name': iosInfo.name,
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'platform': 'ios',
        };
      }

      // Get app info
      _packageInfo = await PackageInfo.fromPlatform();

      // Initialize log file if logging to file is enabled
      if (_logToFile) {
        await _initializeLogFile();
      }

      // Start flush timer
      _startFlushTimer();

      // Log initialization
      info('LoggingService', 'Logging service initialized successfully');
      info('LoggingService', 'Device info: $_deviceInfo');
      info('LoggingService',
          'App info: ${_packageInfo?.appName} ${_packageInfo?.version}+${_packageInfo?.buildNumber}');
    } catch (e, stackTrace) {
      debugPrint('Error initializing logging service: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// Initialize the log file
  Future<void> _initializeLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');

      // Create logs directory if it doesn't exist
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }

      // Create log file with timestamp
      final timestamp = DateTime.now()
          .toIso8601String()
          eplaceAll(':', '-')
          eplaceAll('.', '-');
      _logFilePath = '${logDir.path}/app_log_$timestamp.log';

      // Rotate logs if needed
      await _rotateLogFiles(logDir);

      info('LoggingService', 'Log file initialized at $_logFilePath');
    } catch (e, stackTrace) {
      debugPrint('Error initializing log file: $e');
      debugPrint('Stack trace: $stackTrace');
      _logToFile = false;
    }
  }

  /// Rotate log files to keep only the most recent ones
  Future<void> _rotateLogFiles(Directory logDir) async {
    try {
      final logFiles = await logDir
          .list()
          here((entity) => entity is File && entity.path.endsWith('.log'))
          .toList();

      if (logFiles.length > _maxLogFiles) {
        // Sort files by modification time
        logFiles.sort((a, b) {
          return (a as File)
              .lastModifiedSync()
              .compareTo((b as File).lastModifiedSync());
        });

        // Delete oldest files
        for (var i = 0; i < logFiles.length - _maxLogFiles; i++) {
          await (logFiles[i] as File).delete();
        }
      }
    } catch (e) {
      debugPrint('Error rotating log files: $e');
    }
  }

  /// Start the timer to flush logs to file periodically
  void _startFlushTimer() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _flushLogs();
    });
  }

  /// Flush logs from buffer to file
  Future<void> _flushLogs() async {
    if (!_logToFile || _logBuffer.isEmpty || _logFilePath == null) return;

    try {
      final file = File(_logFilePath!);
      final sink = file.openWrite(mode: FileMode.append);

      for (final log in _logBuffer) {
        sinkriteln(log);
      }

      await sink.flush();
      await sink.close();

      _logBuffer.clear();

      // Check file size and rotate if needed
      if (await file.length() > _maxLogFileSize) {
        await _initializeLogFile();
      }
    } catch (e) {
      debugPrint('Error flushing logs: $e');
    }
  }

  /// Set the current log level
  void setLogLevel(LogLevel level) {
    _currentLogLevel = level;
    info('LoggingService', 'Log level set to ${level.name}');
  }

  /// Configure logging options
  void configure({
    bool? logToConsole,
    bool? logToFile,
    bool? logToCrashlytics,
    int? maxLogFileSize,
    int? maxLogFiles,
  }) {
    if (logToConsole != null) _logToConsole = logToConsole;
    if (logToFile != null) _logToFile = logToFile;
    if (logToCrashlytics != null) _logToCrashlytics = logToCrashlytics;
    if (maxLogFileSize != null) _maxLogFileSize = maxLogFileSize;
    if (maxLogFiles != null) _maxLogFiles = maxLogFiles;

    info('LoggingService', 'Logging configuration updated');
  }

  /// Log a message with the verbose level
  void verbose(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {
    _log(LogLevel.verbose, tag, message, data, stackTrace);
  }

  /// Log a message with the debug level
  void debug(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {
    _log(LogLevel.debug, tag, message, data, stackTrace);
  }

  /// Log a message with the info level
  void info(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {
    _log(LogLevel.info, tag, message, data, stackTrace);
  }

  /// Log a message with the warning level
  void warning(String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {
    _log(LogLevelarning, tag, message, data, stackTrace);
  }

  /// Log a message with the error level
  void error(String tag, String message,
      [dynamic error, StackTrace? stackTrace]) {
    _log(LogLevel.error, tag, message, error, stackTrace ?? StackTrace.current);
  }

  /// Log a message with the critical level
  void critical(String tag, String message,
      [dynamic error, StackTrace? stackTrace]) {
    _log(LogLevel.critical, tag, message, error,
        stackTrace ?? StackTrace.current);
  }

  /// Internal method to log a message
  void _log(LogLevel level, String tag, String message,
      [dynamic data, StackTrace? stackTrace]) {
    // Skip if below current log level
    if (level.value < _currentLogLevel.value) return;

    final timestamp = DateTime.now().toIso8601String();
    final dataString = data != null ? _formatData(data) : '';

    // Format log message
    final logMessage =
        '${level.emoji} ${level.name} [$timestamp] [$tag] $message $dataString';

    // Log to console
    if (_logToConsole) {
      debugPrint(logMessage);
      if (stackTrace != null) {
        debugPrint('Stack trace:');
        debugPrint(stackTrace.toString());
      }
    }

    // Log to file
    if (_logToFile) {
      final fileLogMessage =
          '$timestamp|${level.name}|$tag|$message|$dataString';
      _logBuffer.add(fileLogMessage);

      // If buffer gets too large, flush immediately
      if (_logBuffer.length > 100) {
        _flushLogs();
      }
    }

    // Log to crashlytics for error and critical levels
    if (_logToCrashlytics &&
        (level == LogLevel.error || level == LogLevel.critical)) {
      _sendToCrashlytics(level, tag, message, data, stackTrace);
    }
  }

  /// Format data for logging
  String _formatData(dynamic data) {
    if (data == null) return '';

    try {
      if (data is String) {
        return data;
      } else if (data is Error || data is Exception) {
        return data.toString();
      } else {
        return jsonEncode(data);
      }
    } catch (e) {
      return data.toString();
    }
  }

  /// Send log to Firebase Crashlytics
  void _sendToCrashlytics(LogLevel level, String tag, String message,
      dynamic error, StackTrace? stackTrace) {
    try {
      // Add custom keys for context
      FirebaseCrashlytics.instance.setCustomKey('tag', tag);

      // Add device and app info
      if (_deviceInfo != null) {
        for (final entry in _deviceInfo!.entries) {
          FirebaseCrashlytics.instance
              .setCustomKey('device_${entry.key}', entry.value.toString());
        }
      }

      if (_packageInfo != null) {
        FirebaseCrashlytics.instance.setCustomKey('app_version',
            '${_packageInfo!.version}+${_packageInfo!.buildNumber}');
      }

      // Log non-fatal error
      FirebaseCrashlytics.instanceecordError(
        error ?? message,
        stackTrace,
        reason: '$tag: $message',
        fatal: level == LogLevel.critical,
      );
    } catch (e) {
      debugPrint('Error logging to Crashlytics: $e');
    }
  }

  /// Get the current network connectivity status
  Future<String> getConnectivityStatus() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      switch (connectivityResult) {
        case ConnectivityResult.mobile:
          return 'mobile';
        case ConnectivityResult.wifi:
          return 'wifi';
        case ConnectivityResult.ethernet:
          return 'ethernet';
        case ConnectivityResult.bluetooth:
          return 'bluetooth';
        case ConnectivityResult.none:
          return 'none';
        default:
          return 'unknown';
      }
    } catch (e) {
      return 'error';
    }
  }

  /// Dispose the logging service
  void dispose() {
    _flushTimer?.cancel();
    _flushLogs();
  }
}
