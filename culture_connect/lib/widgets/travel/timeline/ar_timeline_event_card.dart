import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/models/ar/ar_content_marker.dart';
import 'package:culture_connect/providers/ar/ar_content_providers.dart';

/// A card for displaying a timeline event with AR integration
class ARTimelineEventCard extends ConsumerStatefulWidget {
  /// The event to display
  final TimelineEvent event;

  /// The theme of the timeline
  final TimelineTheme theme;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Callback when the AR content is tapped
  final VoidCallback? onARContentTap;

  /// Creates a new AR timeline event card
  const ARTimelineEventCard({
    super.key,
    required this.event,
    required this.theme,
    this.onTap,
    this.onARContentTap,
  });

  @override
  ConsumerState<ARTimelineEventCard> createState() =>
      _ARTimelineEventCardState();
}

class _ARTimelineEventCardState extends ConsumerState<ARTimelineEventCard>
    with SingleTickerProviderStateMixin {
  /// Animation controller for AR badge
  late AnimationController _animationController;

  /// Whether the card is expanded
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Start animation if the event has AR content
    if (widget.eventasARContent) {
      _animationControllerepeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ARTimelineEventCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation if AR content status changed
    if (widget.eventasARContent != oldWidget.eventasARContent) {
      if (widget.eventasARContent) {
        _animationControllerepeat(reverse: true);
      } else {
        _animationController.stop();
      }
    }
  }

  /// Build the event time
  Widget _buildEventTime() {
    if (widget.event.eventTime == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 12,
          color: Colors.grey[600],
        ),
        SizedBox(width: 4),
        Text(
          widget.event.formattedTime!,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        if (widget.event.endTime != null) ...[
          SizedBox(width: 4),
          Text(
            '-',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(width: 4),
          Text(
            '${widget.event.endTime!our.toString().padLeft(2, '0')}:${widget.event.endTime!.minute.toString().padLeft(2, '0')}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  /// Build the event location
  Widget _buildEventLocation() {
    if (widget.event.location == null) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Icon(
          Icons.location_on,
          size: 12,
          color: Colors.grey[600],
        ),
        SizedBox(width: 4),
        Expanded(
          child: Text(
            widget.event.location!,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// Build the AR content badge
  Widget _buildARContentBadge() {
    if (!widget.event.hasARContent || widget.event.arContentId == null) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: widget.onARContentTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue
                  .withAlpha((25 + 13 * _animationController.value).round()),
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.blue.withAlpha(
                    (128 + 127 * _animationController.value).round()),
                width: 2,
              ),
            ),
            child: child,
          );
        },
        child: Center(
          child: Icon(
            Icons.view_in_ar,
            color: Colors.blue,
            size: 20,
          ),
        ),
      ),
    );
  }

  /// Build the AR content preview
  Widget _buildARContentPreview() {
    if (!_isExpanded ||
        !widget.event.hasARContent ||
        widget.event.arContentId == null) {
      return const SizedBox.shrink();
    }

    final arContentMarkerAsync =
        ref.watch(arContentMarkerProvider(widget.event.arContentId!));

    return arContentMarkerAsync.when(
      data: (marker) {
        if (marker == null) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: EdgeInsets.only(top: 8),
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(13),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue.withAlpha(51),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // AR type icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: marker.contentType.color.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    marker.contentType.icon,
                    color: marker.contentType.color,
                    size: 16,
                  ),
                ),
              ),

              SizedBox(width: 8),

              // AR content info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      marker.title,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 2),
                    Text(
                      marker.contentType.displayName,
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),

              // View button
              TextButton(
                onPressed: widget.onARContentTap,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  minimumSize: Size(60, 24),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text(
                  'Preview',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
        ).animate().fadeIn(
              duration: const Duration(milliseconds: 300),
            );
      },
      loading: () => Container(
        margin: EdgeInsets.only(top: 8),
        height: 48,
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
          ),
        ),
      ),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: widget.eventasARContent
            ? BorderSide(
                color: Colors.blueithAlpha(77),
                width: 1,
              )
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  // Event icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: widget.event.typeColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        widget.event.typeIcon,
                        color: widget.event.typeColor,
                        size: 20,
                      ),
                    ),
                  ),

                  SizedBox(width: 12),

                  // Title and time
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          widget.event.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        SizedBox(height: 4),

                        // Time
                        _buildEventTime(),

                        SizedBox(height: 4),

                        // Location
                        _buildEventLocation(),
                      ],
                    ),
                  ),

                  // AR content badge
                  if (widget.event.hasARContent) ...[
                    _buildARContentBadge(),
                  ],
                ],
              ),

              // Description
              if (widget.event.description != null && _isExpanded) ...[
                SizedBox(height: 8),
                Text(
                  widget.event.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // AR content preview
              _buildARContentPreview(),

              // Expand/collapse button
              if (widget.event.description != null ||
                  widget.event.hasARContent) ...[
                SizedBox(height: 4),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () {
                      setState(() {
                        _isExpanded = !_isExpanded;
                      });
                    },
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: Size(60, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isExpanded ? 'Less' : 'More',
                          style: TextStyle(
                            fontSize: 12,
                          ),
                        ),
                        Icon(
                          _isExpanded
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
