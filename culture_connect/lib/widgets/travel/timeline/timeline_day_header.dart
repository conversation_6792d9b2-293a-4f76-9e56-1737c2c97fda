import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/travel/timeline.dart';

/// A header for a day in a timeline
class TimelineDayHeader extends StatelessWidget {
  /// The date to display
  final DateTime date;

  /// The theme of the timeline
  final TimelineTheme theme;

  /// Creates a new timeline day header
  const TimelineDayHeader({
    super.key,
    required this.date,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    // Date formats
    final dayFormat = DateFormat('EEEE');
    final dateFormat = DateFormat('MMMM d, yyyy');

    return Container(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: theme.primaryColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Day number
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.primaryColor,
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                date.day.toString(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: theme.primaryColor,
                ),
              ),
            ),
          ),

          SizedBox(width: 16),

          // Day and date
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Day of week
                Text(
                  dayFormat.format(date),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                SizedBox(height: 2),

                // Full date
                Text(
                  dateFormat.format(date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withAlpha(204),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
