import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/travel/timeline.dart';
import 'package:culture_connect/models/travel/timeline_event.dart';
import 'package:culture_connect/widgets/travel/timeline/timeline_event_card.dart';
import 'package:culture_connect/widgets/travel/timeline/timeline_day_header.dart';

/// A widget for displaying a timeline
class TimelineView extends ConsumerStatefulWidget {
  /// The timeline to display
  final Timeline timeline;

  /// Whether to show the timeline header
  final bool showHeader;

  /// Whether to allow editing
  final bool allowEditing;

  /// Callback when an event is tapped
  final Function(TimelineEvent)? onEventTap;

  /// Callback when an event is long pressed
  final Function(TimelineEvent)? onEventLongPress;

  /// Callback when an event is dragged and dropped
  final Function(TimelineEvent, DateTime)? onEventDragEnd;

  /// Creates a new timeline view
  const TimelineView({
    super.key,
    required this.timeline,
    this.showHeader = true,
    this.allowEditing = false,
    this.onEventTap,
    this.onEventLongPress,
    this.onEventDragEnd,
  });

  @override
  ConsumerState<TimelineView> createState() => _TimelineViewState();
}

class _TimelineViewState extends ConsumerState<TimelineView> {
  /// The currently selected date
  late DateTime _selectedDate;

  /// The scroll controller
  final ScrollController _scrollController = ScrollController();

  /// The date format
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  /// The day format
  final DateFormat _dayFormat = DateFormat('EEE');

  /// The month format
  final DateFormat _monthFormat = DateFormat('MMM');

  /// The day number format
  final DateFormat _dayNumberFormat = DateFormat('d');

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.timeline.startDate;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Build the timeline header
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: widget.timeline.theme.primaryColor.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.timeline.title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: widget.timeline.theme.primaryColor,
            ),
          ),

          SizedBox(height: 4),

          // Description
          if (widget.timeline.description != null) ...[
            Text(
              widget.timeline.description!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8),
          ],

          // Date range
          Row(
            children: [
              Icon(
                Icons.date_range,
                size: 16,
                color: widget.timeline.theme.primaryColor,
              ),
              SizedBox(width: 4),
              Text(
                '${_dateFormat.format(widget.timeline.startDate)} - ${_dateFormat.format(widget.timeline.endDate)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),

          SizedBox(height: 4),

          // Duration
          Row(
            children: [
              Icon(
                Icons.timelapse,
                size: 16,
                color: widget.timeline.theme.primaryColor,
              ),
              SizedBox(width: 4),
              Text(
                '${widget.timeline.durationInDays} ${widget.timeline.durationInDays == 1 ? 'day' : 'days'}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build the date selector
  Widget _buildDateSelector() {
    final dates = <DateTime>[];

    // Generate dates
    for (int i = 0; i < widget.timeline.durationInDays; i++) {
      dates.add(widget.timeline.startDate.add(Duration(days: i)));
    }

    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: dates.length,
        itemBuilder: (context, index) {
          final date = dates[index];
          final isSelected =
              _dateFormat.format(date) == _dateFormat.format(_selectedDate);

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedDate = date;
              });

              // Scroll to the selected date
              _scrollToDate(date);
            },
            child: Container(
              width: 60,
              margin: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected
                    ? widget.timeline.theme.primaryColor
                    : Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected
                      ? widget.timeline.theme.primaryColor
                      : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Day of week
                  Text(
                    _dayFormat.format(date),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),

                  SizedBox(height: 4),

                  // Day number
                  Text(
                    _dayNumberFormat.format(date),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),

                  SizedBox(height: 4),

                  // Month
                  Text(
                    _monthFormat.format(date),
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? Colors.white : Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build the timeline content
  Widget _buildTimelineContent() {
    // Group events by date
    final eventsByDate = <String, List<TimelineEvent>>{};

    for (final event in widget.timeline.events) {
      final dateString = _dateFormat.format(event.eventDate);

      if (!eventsByDate.containsKey(dateString)) {
        eventsByDate[dateString] = [];
      }

      eventsByDate[dateString]!.add(event);
    }

    // Sort dates
    final sortedDates = eventsByDate.keys.toList()..sort();

    // Build timeline
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.all(16),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateString = sortedDates[index];
        final date = DateTime.parse(dateString);
        final events = eventsByDate[dateString]!;

        // Sort events by time
        events.sort((a, b) {
          if (a.eventTime == null && b.eventTime == null) return 0;
          if (a.eventTime == null) return -1;
          if (b.eventTime == null) return 1;

          final aMinutes = a.eventTime!.hour * 60 + a.eventTime!.minute;
          final bMinutes = b.eventTime!.hour * 60 + b.eventTime!.minute;
          return aMinutes.compareTo(bMinutes);
        });

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Day header
            TimelineDayHeader(
              date: date,
              theme: widget.timeline.theme,
            ),

            SizedBox(height: 8),

            // Events
            ...events.map((event) {
              return Padding(
                padding: EdgeInsets.only(bottom: 8),
                child: TimelineEventCard(
                  event: event,
                  theme: widget.timeline.theme,
                  allowDrag: widget.allowEditing,
                  onTap: () {
                    if (widget.onEventTap != null) {
                      widget.onEventTap!(event);
                    }
                  },
                  onLongPress: () {
                    if (widget.onEventLongPress != null) {
                      widget.onEventLongPress!(event);
                    }
                  },
                  onDragEnd: (details) {
                    if (widget.onEventDragEnd != null) {
                      // Calculate the new date based on the drag position
                      // This is a simplified implementation
                      final newDate = date;
                      widget.onEventDragEnd!(event, newDate);
                    }
                  },
                ),
              );
            }),

            SizedBox(height: 16),
          ],
        );
      },
    );
  }

  /// Scroll to a specific date
  void _scrollToDate(DateTime date) {
    // Find the index of the date
    final dateString = _dateFormat.format(date);

    // Group events by date
    final eventsByDate = <String, List<TimelineEvent>>{};

    for (final event in widget.timeline.events) {
      final eventDateString = _dateFormat.format(event.eventDate);

      if (!eventsByDate.containsKey(eventDateString)) {
        eventsByDate[eventDateString] = [];
      }

      eventsByDate[eventDateString]!.add(event);
    }

    // Sort dates
    final sortedDates = eventsByDate.keys.toList()..sort();

    // Find the index of the date
    final index = sortedDates.indexOf(dateString);

    if (index != -1) {
      // Calculate the scroll position
      final position = index * 200; // Approximate height of a day section

      // Scroll to the position
      _scrollController.animateTo(
        position.toDouble(),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        if (widget.showHeader) ...[
          _buildHeader(),
          SizedBox(height: 16),
        ],

        // Date selector
        _buildDateSelector(),

        // Timeline content
        Expanded(
          child: _buildTimelineContent(),
        ),
      ],
    );
  }
}
